{"fuzzing": {"workers": 16, "workerResetLimit": 50, "timeout": 0, "testLimit": 0, "callSequenceLength": 100, "corpusDirectory": "medusa", "coverageEnabled": true, "deploymentOrder": [], "targetContracts": ["CryticTester"], "targetContractsBalances": ["0x27b46536c66c8e3000000"], "predeployedContracts": {"CryticTester": "******************************************"}, "constructorArgs": {}, "deployerAddress": "******************************************", "senderAddresses": ["0x10000", "0x20000", "0x30000"], "blockNumberDelayMax": 60480, "blockTimestampDelayMax": 604800, "blockGasLimit": 125000000, "transactionGasLimit": 12500000, "testing": {"stopOnFailedTest": false, "stopOnFailedContractMatching": false, "stopOnNoTests": true, "testAllContracts": false, "traceAll": false, "assertionTesting": {"enabled": true, "testViewMethods": true, "panicCodeConfig": {"failOnCompilerInsertedPanic": false, "failOnAssertion": true, "failOnArithmeticUnderflow": false, "failOnDivideByZero": false, "failOnEnumTypeConversionOutOfBounds": false, "failOnIncorrectStorageAccess": false, "failOnPopEmptyArray": false, "failOnOutOfBoundsArrayAccess": false, "failOnAllocateTooMuchMemory": false, "failOnCallUninitializedVariable": false}}, "propertyTesting": {"enabled": true, "testPrefixes": ["echidna_"]}, "optimizationTesting": {"enabled": false, "testPrefixes": ["optimize_"]}}, "chainConfig": {"codeSizeCheckDisabled": true, "cheatCodes": {"cheatCodesEnabled": true, "enableFFI": false}, "skipAccountChecks": true, "forkConfig": {"forkModeEnabled": false, "rpcUrl": "", "rpcBlock": 1, "poolSize": 20}}}, "compilation": {"platform": "crytic-compile", "platformConfig": {"target": ".", "solcVersion": "", "exportDirectory": "", "args": ["--foundry-compile-all"]}}, "slither": {"useSlither": true, "cachePath": "slither_results.json", "args": []}, "logging": {"level": "info", "logDirectory": ""}}