name: Build Docker Image

on:
  push:
    branches:
      - '**'

jobs:
  build-arm64:
    runs-on:  ubuntu-24.04-arm
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          submodules: true

      - name: Setup buildx (arm64)
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/arm64

      - name: Docker meta (arm64)
        id: meta_arm64
        uses: docker/metadata-action@v5
        with:
          images: ${{ secrets.DOCKER_USERNAME }}/contracts
          tags: |
            type=ref,event=branch,suffix=-arm64 # Add suffix for temporary tag
            type=sha,suffix=-arm64 # Add suffix for temporary tag

      - uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Login to Docker Hub (arm64)
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker image (arm64)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta_arm64.outputs.tags }}
          labels: ${{ steps.meta_arm64.outputs.labels }}
          platforms: linux/arm64

  build-amd64:
    runs-on:  ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          submodules: true

      - name: Setup buildx (amd64)
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/amd64

      - name: Docker meta (amd64)
        id: meta_amd64
        uses: docker/metadata-action@v5
        with:
          images: ${{ secrets.DOCKER_USERNAME }}/contracts
          tags: |
            type=ref,event=branch,suffix=-amd64 # Add suffix for temporary tag
            type=sha,suffix=-amd64 # Add suffix for temporary tag

      - uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Login to Docker Hub (amd64)
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker image (amd64)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta_amd64.outputs.tags }}
          labels: ${{ steps.meta_amd64.outputs.labels }}
          platforms: linux/amd64
